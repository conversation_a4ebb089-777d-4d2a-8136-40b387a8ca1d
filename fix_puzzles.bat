@echo off
chcp 65001 >nul
title إصلاح ملف الألغاز - Code Puzzle Game
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 إصلاح ملف الألغاز 🔧                  ║
echo ║                  إصلاح مشكلة puzzles.js                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري فحص ملف puzzles.js...
echo.

if not exist "puzzles.js" (
    echo ❌ ملف puzzles.js غير موجود!
    echo 💡 تأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ ملف puzzles.js موجود
echo.

echo 🔧 جاري إصلاح المشاكل الشائعة...
echo.

rem إنشاء نسخة احتياطية
copy "puzzles.js" "puzzles.js.backup" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم إنشاء نسخة احتياطية: puzzles.js.backup
) else (
    echo ⚠️  فشل في إنشاء نسخة احتياطية
)

rem إصلاح الفواصل المفقودة باستخدام PowerShell
powershell -Command "& {
    $content = Get-Content 'puzzles.js' -Raw -Encoding UTF8
    
    # إصلاح الفواصل المفقودة
    $content = $content -replace '}\s*\n\s*{', '},`n            {'
    
    # إصلاح مشاكل أخرى شائعة
    $content = $content -replace '}\s*\]\s*}', '}]}'
    $content = $content -replace '}\s*,\s*\]\s*}', '}]}'
    
    # حفظ الملف المصحح
    $content | Out-File 'puzzles.js' -Encoding UTF8 -NoNewline
    
    Write-Host '✅ تم إصلاح الفواصل المفقودة'
}"

echo.
echo 🧪 جاري اختبار الملف المصحح...
echo.

rem اختبار الملف باستخدام Node.js إذا كان متاحاً
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo 🟡 جاري اختبار الملف بـ Node.js...
    node -e "try { require('./puzzles.js'); console.log('✅ ملف puzzles.js يعمل بشكل صحيح'); } catch(e) { console.log('❌ خطأ في الملف:', e.message); process.exit(1); }"
    if %errorlevel% equ 0 (
        echo ✅ الملف يعمل بشكل صحيح!
    ) else (
        echo ❌ ما زال هناك خطأ في الملف
        echo 🔄 جاري استعادة النسخة الاحتياطية...
        copy "puzzles.js.backup" "puzzles.js" >nul 2>&1
        echo ⚠️  تم استعادة النسخة الأصلية
    )
) else (
    echo 🟡 Node.js غير متاح، سيتم اختبار الملف في المتصفح
)

echo.
echo 🌐 جاري فتح صفحة اختبار الألغاز...
start "" "http://localhost:8000/test_puzzles.html"

echo.
echo 📋 تعليمات إضافية:
echo.
echo 1. إذا ظهرت أخطاء في صفحة الاختبار:
echo    - افتح ملف puzzles.js في محرر نصوص
echo    - ابحث عن الأخطاء الإملائية أو الفواصل المفقودة
echo    - تأكد من وجود فاصلة بعد كل } قبل {
echo.
echo 2. إذا استمرت المشاكل:
echo    - استخدم النسخة الاحتياطية: puzzles.js.backup
echo    - أو أعد إنشاء الملف من جديد
echo.
echo 3. للتشغيل السريع:
echo    - استخدم start_game.bat
echo    - أو افتح fix_game.html للتشخيص الشامل
echo.

pause
