// إعدادات لعبة Code Puzzle
// يمكنك تعديل هذه الإعدادات لتخصيص اللعبة

const gameConfig = {
    // إعدادات النقاط
    scoring: {
        baseScore: 100,           // النقاط الأساسية لكل إجابة صحيحة
        difficultyMultipliers: {
            easy: 1,              // مضاعف المستوى السهل
            medium: 1.5,          // مضاعف المستوى المتوسط
            hard: 2               // مضاعف المستوى الصعب
        },
        timeBonus: true,          // مكافأة الوقت
        timeBonusMultiplier: 0.1  // مضاعف مكافأة الوقت
    },

    // إعدادات الوقت
    timing: {
        autoNextDelay: 2000,      // التأخير قبل الانتقال للسؤال التالي (بالميلي ثانية)
        hintDelay: 1000,          // التأخير قبل إظهار التلميح
        maxTimePerQuestion: 300   // الحد الأقصى للوقت لكل سؤال (بالثواني، 0 = بلا حدود)
    },

    // إعدادات الواجهة
    ui: {
        theme: 'default',         // السمة: default, dark, light
        language: 'ar',           // اللغة: ar, en
        animations: true,         // تفعيل الحركات
        sounds: false,            // تفعيل الأصوات (قريباً)
        showProgress: true,       // إظهار شريط التقدم
        showTimer: true           // إظهار المؤقت
    },

    // إعدادات اللعبة
    gameplay: {
        allowSkip: true,          // السماح بتخطي الأسئلة
        allowHints: true,         // السماح بالتلميحات
        allowSolutions: true,     // السماح بعرض الحلول
        shuffleQuestions: false,  // خلط ترتيب الأسئلة
        caseSensitive: false,     // حساسية الأحرف في الإجابات
        strictAnswers: false      // الإجابات الصارمة (بدون تسامح في الأخطاء الإملائية)
    },

    // إعدادات التخزين
    storage: {
        saveBestScores: true,     // حفظ أفضل النتائج
        saveProgress: true,       // حفظ التقدم
        saveSettings: true        // حفظ الإعدادات
    },

    // إعدادات التطوير
    development: {
        debugMode: false,         // وضع التطوير
        showConsoleInfo: true,    // إظهار معلومات في وحدة التحكم
        enableTestMode: false     // تفعيل وضع الاختبار
    },

    // رسائل مخصصة
    messages: {
        welcome: "مرحباً بك في تحدي البرمجة!",
        correct: "إجابة صحيحة! أحسنت! 🎉",
        incorrect: "إجابة خاطئة. حاول مرة أخرى!",
        gameComplete: "تهانينا! لقد أكملت جميع الأسئلة!",
        newRecord: "🎉 رقم قياسي جديد! تهانينا!",
        noAnswerProvided: "يرجى إدخال إجابة قبل الإرسال.",
        confirmSkip: "هل أنت متأكد من تخطي هذا السؤال؟"
    },

    // ألوان مخصصة (CSS Variables)
    colors: {
        primary: '#667eea',
        secondary: '#764ba2',
        success: '#48bb78',
        error: '#f56565',
        warning: '#ed8936',
        info: '#4299e1'
    }
};

// دالة لتطبيق الإعدادات
function applyGameConfig() {
    // تطبيق الألوان المخصصة
    const root = document.documentElement;
    Object.entries(gameConfig.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
    });

    // تطبيق إعدادات الواجهة
    if (!gameConfig.ui.animations) {
        document.body.classList.add('no-animations');
    }

    if (gameConfig.ui.theme !== 'default') {
        document.body.classList.add(`theme-${gameConfig.ui.theme}`);
    }

    // طباعة معلومات التطوير
    if (gameConfig.development.showConsoleInfo) {
        console.log('🎮 Code Puzzle Game');
        console.log('⚙️ الإعدادات المطبقة:', gameConfig);
    }
}

// دالة للحصول على إعداد معين
function getConfig(path) {
    const keys = path.split('.');
    let value = gameConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return undefined;
        }
    }
    
    return value;
}

// دالة لتحديث إعداد معين
function setConfig(path, newValue) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = gameConfig;
    
    for (const key of keys) {
        if (!(key in target)) {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = newValue;
    
    // حفظ الإعدادات إذا كان مفعلاً
    if (gameConfig.storage.saveSettings) {
        localStorage.setItem('codePuzzleConfig', JSON.stringify(gameConfig));
    }
}

// دالة لتحميل الإعدادات المحفوظة
function loadSavedConfig() {
    if (gameConfig.storage.saveSettings) {
        const saved = localStorage.getItem('codePuzzleConfig');
        if (saved) {
            try {
                const savedConfig = JSON.parse(saved);
                // دمج الإعدادات المحفوظة مع الافتراضية
                Object.assign(gameConfig, savedConfig);
            } catch (error) {
                console.warn('فشل في تحميل الإعدادات المحفوظة:', error);
            }
        }
    }
}

// دالة لإعادة تعيين الإعدادات للافتراضية
function resetConfig() {
    localStorage.removeItem('codePuzzleConfig');
    location.reload(); // إعادة تحميل الصفحة لتطبيق الإعدادات الافتراضية
}

// تحميل الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    loadSavedConfig();
    applyGameConfig();
});

// تصدير الإعدادات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { gameConfig, getConfig, setConfig, loadSavedConfig, resetConfig };
}
