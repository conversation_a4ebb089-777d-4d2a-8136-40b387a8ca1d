<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل اللعبة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
        .fix-section.error {
            background: #fff5f5;
            border-color: #feb2b2;
        }
        .fix-section.success {
            background: #f0fff4;
            border-color: #9ae6b4;
        }
        .fix-section.warning {
            background: #fffbeb;
            border-color: #fbbf24;
        }
        .fix-btn {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.3s ease;
        }
        .fix-btn:hover {
            transform: translateY(-2px);
        }
        .fix-btn.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح مشاكل Code Puzzle Game</h1>
            <p>أداة تشخيص وإصلاح المشاكل الشائعة</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar"></div>
        </div>

        <div id="fixes-container">
            <!-- سيتم إضافة الإصلاحات هنا -->
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="fix-btn" onclick="runAllFixes()">🔄 تشغيل جميع الإصلاحات</button>
            <button class="fix-btn" onclick="testGame()">🧪 اختبار اللعبة</button>
            <button class="fix-btn" onclick="openGame()">🎮 فتح اللعبة</button>
        </div>
    </div>

    <script>
        const fixes = [
            {
                name: 'فحص ملف puzzles.js',
                description: 'التحقق من وجود وصحة ملف الألغاز',
                fix: async function() {
                    try {
                        const response = await fetch('puzzles.js');
                        if (!response.ok) {
                            throw new Error('ملف puzzles.js غير موجود');
                        }
                        
                        const content = await response.text();
                        
                        // فحص الأخطاء الشائعة
                        const issues = [];
                        
                        if (!content.includes('const puzzles = {')) {
                            issues.push('متغير puzzles غير موجود');
                        }
                        
                        if (!content.includes('function getAllPuzzles')) {
                            issues.push('دالة getAllPuzzles غير موجودة');
                        }
                        
                        // فحص الفواصل المفقودة
                        const lines = content.split('\n');
                        const problematicLines = [];
                        
                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i].trim();
                            const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                            
                            if (line.endsWith('}') && nextLine.startsWith('{') && !line.endsWith('},')) {
                                problematicLines.push(i + 1);
                            }
                        }
                        
                        if (problematicLines.length > 0) {
                            issues.push(`فواصل مفقودة في الأسطر: ${problematicLines.join(', ')}`);
                        }
                        
                        if (issues.length > 0) {
                            return { success: false, message: issues.join('; ') };
                        }
                        
                        return { success: true, message: 'ملف puzzles.js صحيح' };
                    } catch (error) {
                        return { success: false, message: error.message };
                    }
                }
            },
            {
                name: 'فحص ملف script.js',
                description: 'التحقق من ملف JavaScript الرئيسي',
                fix: async function() {
                    try {
                        const response = await fetch('script.js');
                        if (!response.ok) {
                            throw new Error('ملف script.js غير موجود');
                        }
                        
                        const content = await response.text();
                        
                        if (!content.includes('class CodePuzzleGame')) {
                            return { success: false, message: 'كلاس CodePuzzleGame غير موجود' };
                        }
                        
                        if (!content.includes('DOMContentLoaded')) {
                            return { success: false, message: 'مستمع DOMContentLoaded غير موجود' };
                        }
                        
                        return { success: true, message: 'ملف script.js صحيح' };
                    } catch (error) {
                        return { success: false, message: error.message };
                    }
                }
            },
            {
                name: 'فحص ملف styles.css',
                description: 'التحقق من ملف التنسيق',
                fix: async function() {
                    try {
                        const response = await fetch('styles.css');
                        if (!response.ok) {
                            throw new Error('ملف styles.css غير موجود');
                        }
                        
                        return { success: true, message: 'ملف styles.css موجود' };
                    } catch (error) {
                        return { success: false, message: error.message };
                    }
                }
            },
            {
                name: 'فحص ملف config.js',
                description: 'التحقق من ملف الإعدادات',
                fix: async function() {
                    try {
                        const response = await fetch('config.js');
                        if (!response.ok) {
                            return { success: false, message: 'ملف config.js غير موجود (اختياري)' };
                        }
                        
                        return { success: true, message: 'ملف config.js موجود' };
                    } catch (error) {
                        return { success: false, message: 'ملف config.js غير موجود (اختياري)' };
                    }
                }
            },
            {
                name: 'اختبار تحميل الألغاز',
                description: 'اختبار تحميل وتشغيل دوال الألغاز',
                fix: async function() {
                    try {
                        // تحميل ملف الألغاز
                        const script = document.createElement('script');
                        script.src = 'puzzles.js';
                        
                        return new Promise((resolve) => {
                            script.onload = function() {
                                try {
                                    if (typeof puzzles === 'undefined') {
                                        resolve({ success: false, message: 'متغير puzzles غير محمل' });
                                        return;
                                    }
                                    
                                    if (typeof getAllPuzzles === 'undefined') {
                                        resolve({ success: false, message: 'دالة getAllPuzzles غير محملة' });
                                        return;
                                    }
                                    
                                    // اختبار الدالة
                                    const testPuzzles = getAllPuzzles('java', 'easy');
                                    if (!testPuzzles || testPuzzles.length === 0) {
                                        resolve({ success: false, message: 'لا توجد ألغاز في java/easy' });
                                        return;
                                    }
                                    
                                    resolve({ success: true, message: `تم تحميل ${testPuzzles.length} ألغاز بنجاح` });
                                } catch (error) {
                                    resolve({ success: false, message: error.message });
                                }
                            };
                            
                            script.onerror = function() {
                                resolve({ success: false, message: 'فشل في تحميل ملف puzzles.js' });
                            };
                            
                            document.head.appendChild(script);
                        });
                    } catch (error) {
                        return { success: false, message: error.message };
                    }
                }
            },
            {
                name: 'فحص إعدادات المتصفح',
                description: 'التحقق من إعدادات المتصفح المطلوبة',
                fix: async function() {
                    const issues = [];
                    
                    // فحص JavaScript
                    if (typeof window === 'undefined') {
                        issues.push('JavaScript غير مفعل');
                    }
                    
                    // فحص localStorage
                    try {
                        localStorage.setItem('test', 'test');
                        localStorage.removeItem('test');
                    } catch (error) {
                        issues.push('localStorage غير متاح');
                    }
                    
                    // فحص fetch API
                    if (typeof fetch === 'undefined') {
                        issues.push('Fetch API غير متاح');
                    }
                    
                    if (issues.length > 0) {
                        return { success: false, message: issues.join('; ') };
                    }
                    
                    return { success: true, message: 'إعدادات المتصفح صحيحة' };
                }
            }
        ];

        let completedFixes = 0;

        function updateProgress() {
            const percentage = (completedFixes / fixes.length) * 100;
            document.getElementById('progress-bar').style.width = percentage + '%';
        }

        function createFixSection(fix, status, message) {
            const section = document.createElement('div');
            section.className = `fix-section ${status}`;
            
            let icon = '⏳';
            if (status === 'success') icon = '✅';
            else if (status === 'error') icon = '❌';
            else if (status === 'warning') icon = '⚠️';
            
            section.innerHTML = `
                <h3>${icon} ${fix.name}</h3>
                <p>${fix.description}</p>
                <div class="status">${message}</div>
            `;
            
            return section;
        }

        async function runSingleFix(fix, container) {
            const loadingSection = createFixSection(fix, 'warning', 'جاري الفحص...');
            container.appendChild(loadingSection);

            try {
                const result = await fix.fix();
                container.removeChild(loadingSection);
                
                const status = result.success ? 'success' : 'error';
                const resultSection = createFixSection(fix, status, result.message);
                container.appendChild(resultSection);
                
                completedFixes++;
                updateProgress();
                
                return result.success;
            } catch (error) {
                container.removeChild(loadingSection);
                const errorSection = createFixSection(fix, 'error', `خطأ: ${error.message}`);
                container.appendChild(errorSection);
                
                completedFixes++;
                updateProgress();
                
                return false;
            }
        }

        async function runAllFixes() {
            const container = document.getElementById('fixes-container');
            container.innerHTML = '';
            completedFixes = 0;
            updateProgress();

            let allPassed = true;

            for (const fix of fixes) {
                const result = await runSingleFix(fix, container);
                if (!result) allPassed = false;
                
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            // إضافة نتيجة إجمالية
            setTimeout(() => {
                const summarySection = document.createElement('div');
                summarySection.className = `fix-section ${allPassed ? 'success' : 'error'}`;
                summarySection.innerHTML = `
                    <h3>${allPassed ? '🎉' : '⚠️'} النتيجة الإجمالية</h3>
                    <p>${allPassed ? 'جميع الفحوصات نجحت! اللعبة جاهزة للتشغيل.' : 'بعض المشاكل تحتاج إصلاح.'}</p>
                    ${allPassed ? '<button class="fix-btn" onclick="openGame()">🎮 تشغيل اللعبة الآن</button>' : '<button class="fix-btn danger" onclick="showFixInstructions()">📋 عرض تعليمات الإصلاح</button>'}
                `;
                container.appendChild(summarySection);
            }, 500);
        }

        function testGame() {
            window.open('test_puzzles.html', '_blank');
        }

        function openGame() {
            window.open('index.html', '_blank');
        }

        function showFixInstructions() {
            alert(`تعليمات الإصلاح:

1. تأكد من وجود جميع الملفات في نفس المجلد
2. استخدم متصفح حديث (Chrome, Firefox, Edge)
3. تأكد من تفعيل JavaScript
4. جرب فتح اللعبة من خادم محلي بدلاً من فتح الملف مباشرة
5. راجع وحدة التحكم (F12) للمزيد من التفاصيل

إذا استمرت المشاكل، استخدم ملف start_game.bat للتشغيل التلقائي.`);
        }

        // تشغيل الفحص عند تحميل الصفحة
        window.onload = function() {
            runAllFixes();
        };
    </script>
</body>
</html>
