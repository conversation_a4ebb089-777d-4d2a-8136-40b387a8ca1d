// Game State Management
class CodePuzzleGame {
    constructor() {
        this.currentScreen = 'welcome';
        this.selectedDifficulty = null;
        this.selectedLanguage = null;
        this.currentPuzzleIndex = 0;
        this.puzzleList = [];
        this.score = 0;
        this.startTime = null;
        this.correctAnswers = 0;
        this.totalQuestions = 0;
        this.timer = null;
        
        this.initializeEventListeners();
        this.showScreen('welcome');
        this.displayBestScores();
    }

    initializeEventListeners() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectDifficulty(e.target.closest('.difficulty-btn').dataset.difficulty);
            });
        });

        // Language selection
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectLanguage(e.target.closest('.language-btn').dataset.language);
            });
        });

        // Start game button
        document.getElementById('start-game').addEventListener('click', () => {
            this.startGame();
        });

        // Submit answer
        document.getElementById('submit-answer').addEventListener('click', () => {
            this.submitAnswer();
        });

        // Enter key for answer submission
        document.getElementById('user-answer').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitAnswer();
            }
        });

        // Action buttons
        document.getElementById('hint-btn').addEventListener('click', () => {
            this.showHint();
        });

        document.getElementById('skip-btn').addEventListener('click', () => {
            this.skipPuzzle();
        });

        document.getElementById('solution-btn').addEventListener('click', () => {
            this.showSolution();
        });

        // Results screen buttons
        document.getElementById('play-again').addEventListener('click', () => {
            this.resetGame();
        });

        document.getElementById('share-results').addEventListener('click', () => {
            this.shareResults();
        });

        // Modal close
        document.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    selectDifficulty(difficulty) {
        this.selectedDifficulty = difficulty;
        
        // Update UI
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-difficulty="${difficulty}"]`).classList.add('selected');
        
        this.checkStartButtonState();
    }

    selectLanguage(language) {
        this.selectedLanguage = language;
        
        // Update UI
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-language="${language}"]`).classList.add('selected');
        
        this.checkStartButtonState();
    }

    checkStartButtonState() {
        const startBtn = document.getElementById('start-game');
        if (this.selectedDifficulty && this.selectedLanguage) {
            startBtn.disabled = false;
        } else {
            startBtn.disabled = true;
        }
    }

    startGame() {
        this.puzzleList = getAllPuzzles(this.selectedLanguage, this.selectedDifficulty);
        if (this.puzzleList.length === 0) {
            this.showFeedback('عذراً، لا توجد ألغاز متاحة لهذا المستوى حالياً.', 'error');
            return;
        }

        this.currentPuzzleIndex = 0;
        this.score = 0;
        this.correctAnswers = 0;
        this.totalQuestions = this.puzzleList.length;
        this.startTime = new Date();
        
        this.showScreen('game');
        this.startTimer();
        this.loadCurrentPuzzle();
    }

    startTimer() {
        this.timer = setInterval(() => {
            const elapsed = new Date() - this.startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('timer').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    loadCurrentPuzzle() {
        if (this.currentPuzzleIndex >= this.puzzleList.length) {
            this.endGame();
            return;
        }

        const puzzle = this.puzzleList[this.currentPuzzleIndex];
        
        // Update puzzle info
        document.getElementById('puzzle-title').textContent = puzzle.title;
        document.getElementById('puzzle-language').textContent = this.getLanguageDisplayName(this.selectedLanguage);
        document.getElementById('puzzle-difficulty').textContent = this.getDifficultyDisplayName(this.selectedDifficulty);
        document.getElementById('puzzle-description').textContent = puzzle.description;
        document.getElementById('puzzle-code').innerHTML = `<code>${this.highlightCode(puzzle.code)}</code>`;
        document.getElementById('puzzle-question').textContent = puzzle.question;
        
        // Update level
        document.getElementById('level').textContent = this.currentPuzzleIndex + 1;
        
        // Clear previous answer and feedback
        document.getElementById('user-answer').value = '';
        this.hideFeedback();
    }

    highlightCode(code) {
        // Escape HTML first
        let highlighted = code
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');

        // Apply syntax highlighting based on language
        if (this.selectedLanguage === 'java') {
            highlighted = this.highlightJava(highlighted);
        } else if (this.selectedLanguage === 'html') {
            highlighted = this.highlightHTML(highlighted);
        } else if (this.selectedLanguage === 'css') {
            highlighted = this.highlightCSS(highlighted);
        }

        return highlighted
            .replace(/\n/g, '<br>')
            .replace(/  /g, '&nbsp;&nbsp;');
    }

    highlightJava(code) {
        // Java keywords
        const keywords = ['public', 'private', 'protected', 'static', 'final', 'class', 'interface',
                         'extends', 'implements', 'if', 'else', 'for', 'while', 'do', 'switch',
                         'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
                         'finally', 'throw', 'throws', 'new', 'this', 'super', 'void', 'int',
                         'String', 'boolean', 'double', 'float', 'long', 'char'];

        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            code = code.replace(regex, `<span class="keyword">${keyword}</span>`);
        });

        // Strings
        code = code.replace(/"([^"]*)"/g, '<span class="string">"$1"</span>');

        // Comments
        code = code.replace(/\/\/(.*)$/gm, '<span class="comment">//$1</span>');
        code = code.replace(/\/\*([\s\S]*?)\*\//g, '<span class="comment">/*$1*/</span>');

        // Numbers
        code = code.replace(/\b(\d+)\b/g, '<span class="number">$1</span>');

        return code;
    }

    highlightHTML(code) {
        // HTML tags
        code = code.replace(/&lt;(\/?[a-zA-Z][^&gt;]*)&gt;/g, '<span class="keyword">&lt;$1&gt;</span>');

        // Attributes
        code = code.replace(/(\w+)=/g, '<span class="string">$1</span>=');

        // Attribute values
        code = code.replace(/"([^"]*)"/g, '<span class="string">"$1"</span>');

        return code;
    }

    highlightCSS(code) {
        // CSS selectors
        code = code.replace(/^([.#]?[a-zA-Z][a-zA-Z0-9-_]*)\s*{/gm, '<span class="keyword">$1</span> {');

        // CSS properties
        code = code.replace(/(\w+(-\w+)*)\s*:/g, '<span class="string">$1</span>:');

        // CSS values
        code = code.replace(/:\s*([^;]+);/g, ': <span class="number">$1</span>;');

        return code;
    }

    getLanguageDisplayName(language) {
        const names = {
            'java': 'Java',
            'html': 'HTML',
            'css': 'CSS'
        };
        return names[language] || language;
    }

    getDifficultyDisplayName(difficulty) {
        const names = {
            'easy': 'مبتدئ',
            'medium': 'متوسط',
            'hard': 'متقدم'
        };
        return names[difficulty] || difficulty;
    }

    submitAnswer() {
        const userAnswer = document.getElementById('user-answer').value.trim();
        if (!userAnswer) {
            this.showFeedback('يرجى إدخال إجابة قبل الإرسال.', 'error');
            return;
        }

        const currentPuzzle = this.puzzleList[this.currentPuzzleIndex];
        const isCorrect = this.checkAnswer(userAnswer, currentPuzzle.answer);

        if (isCorrect) {
            this.correctAnswers++;
            this.score += this.calculateScore();
            this.showFeedback('إجابة صحيحة! أحسنت! 🎉', 'success');
            
            setTimeout(() => {
                this.nextPuzzle();
            }, 2000);
        } else {
            this.showFeedback(`إجابة خاطئة. الإجابة الصحيحة هي: ${currentPuzzle.answer}`, 'error');
            
            setTimeout(() => {
                this.nextPuzzle();
            }, 3000);
        }

        // Update score display
        document.getElementById('score').textContent = this.score;
    }

    checkAnswer(userAnswer, correctAnswer) {
        // Normalize answers for comparison
        const normalize = (str) => str.toLowerCase().replace(/\s+/g, '').replace(/[^\w\u0600-\u06FF]/g, '');
        return normalize(userAnswer) === normalize(correctAnswer);
    }

    calculateScore() {
        const baseScore = 100;
        const difficultyMultiplier = {
            'easy': 1,
            'medium': 1.5,
            'hard': 2
        };
        
        return Math.round(baseScore * difficultyMultiplier[this.selectedDifficulty]);
    }

    nextPuzzle() {
        this.currentPuzzleIndex++;
        this.loadCurrentPuzzle();
    }

    showHint() {
        const currentPuzzle = this.puzzleList[this.currentPuzzleIndex];
        this.showModal('تلميح', currentPuzzle.hint);
    }

    skipPuzzle() {
        if (confirm('هل أنت متأكد من تخطي هذا السؤال؟')) {
            this.nextPuzzle();
        }
    }

    showSolution() {
        const currentPuzzle = this.puzzleList[this.currentPuzzleIndex];
        this.showModal('الحل', currentPuzzle.solution);
    }

    showModal(title, content) {
        document.getElementById('modal-title').textContent = title;
        document.getElementById('modal-body').textContent = content;
        document.getElementById('modal').style.display = 'block';
    }

    closeModal() {
        document.getElementById('modal').style.display = 'none';
    }

    showFeedback(message, type) {
        const feedbackArea = document.getElementById('feedback-area');
        feedbackArea.textContent = message;
        feedbackArea.className = `feedback-area ${type}`;
    }

    hideFeedback() {
        const feedbackArea = document.getElementById('feedback-area');
        feedbackArea.className = 'feedback-area';
    }

    showScreen(screenName) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(`${screenName}-screen`).classList.add('active');
        this.currentScreen = screenName;
    }

    endGame() {
        this.stopTimer();

        const endTime = new Date();
        const totalTime = endTime - this.startTime;
        const minutes = Math.floor(totalTime / 60000);
        const seconds = Math.floor((totalTime % 60000) / 1000);

        // Save best score
        this.saveBestScore();

        // Update final stats
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-time').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('correct-answers').textContent =
            `${this.correctAnswers}/${this.totalQuestions}`;

        // Calculate and show performance rating
        this.showPerformanceRating();

        this.showScreen('results');
    }

    saveBestScore() {
        const key = `best_score_${this.selectedLanguage}_${this.selectedDifficulty}`;
        const currentBest = localStorage.getItem(key) || 0;

        if (this.score > currentBest) {
            localStorage.setItem(key, this.score);
            // Show new record message
            setTimeout(() => {
                this.showFeedback('🎉 رقم قياسي جديد! تهانينا!', 'success');
            }, 1000);
        }
    }

    getBestScore() {
        const key = `best_score_${this.selectedLanguage}_${this.selectedDifficulty}`;
        return localStorage.getItem(key) || 0;
    }

    displayBestScores() {
        const scoresGrid = document.getElementById('scores-grid');
        const languages = ['java', 'html', 'css'];
        const difficulties = ['easy', 'medium', 'hard'];

        scoresGrid.innerHTML = '';

        languages.forEach(language => {
            difficulties.forEach(difficulty => {
                const key = `best_score_${language}_${difficulty}`;
                const score = localStorage.getItem(key);

                if (score && score > 0) {
                    const scoreItem = document.createElement('div');
                    scoreItem.className = 'score-item';
                    scoreItem.innerHTML = `
                        <div class="language">${this.getLanguageDisplayName(language)}</div>
                        <div class="difficulty">${this.getDifficultyDisplayName(difficulty)}</div>
                        <div class="score">${score}</div>
                    `;
                    scoresGrid.appendChild(scoreItem);
                }
            });
        });

        if (scoresGrid.children.length === 0) {
            scoresGrid.innerHTML = '<p style="text-align: center; color: #718096;">لا توجد نتائج محفوظة بعد</p>';
        }
    }

    showPerformanceRating() {
        const percentage = (this.correctAnswers / this.totalQuestions) * 100;
        const ratingElement = document.getElementById('performance-rating');
        const messageElement = document.getElementById('performance-message');
        
        let stars = '';
        let message = '';
        
        if (percentage >= 90) {
            stars = '⭐⭐⭐⭐⭐';
            message = 'ممتاز! أداء رائع جداً!';
        } else if (percentage >= 70) {
            stars = '⭐⭐⭐⭐';
            message = 'جيد جداً! استمر في التطوير!';
        } else if (percentage >= 50) {
            stars = '⭐⭐⭐';
            message = 'جيد! يمكنك تحسين أدائك أكثر!';
        } else if (percentage >= 30) {
            stars = '⭐⭐';
            message = 'مقبول، تحتاج لمزيد من التدريب!';
        } else {
            stars = '⭐';
            message = 'تحتاج لمراجعة المفاهيم الأساسية!';
        }
        
        ratingElement.textContent = stars;
        messageElement.textContent = message;
    }

    resetGame() {
        this.currentPuzzleIndex = 0;
        this.score = 0;
        this.correctAnswers = 0;
        this.selectedDifficulty = null;
        this.selectedLanguage = null;
        
        // Reset UI
        document.querySelectorAll('.difficulty-btn, .language-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.getElementById('start-game').disabled = true;
        document.getElementById('score').textContent = '0';
        document.getElementById('timer').textContent = '00:00';
        document.getElementById('level').textContent = '1';
        
        this.showScreen('welcome');
        this.displayBestScores();
    }

    shareResults() {
        const text = `لقد حققت ${this.score} نقطة في لعبة Code Puzzle! 🎮\nأجبت على ${this.correctAnswers}/${this.totalQuestions} سؤال بشكل صحيح.\n\nجرب اللعبة بنفسك!`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Code Puzzle - نتائجي',
                text: text
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ النتائج إلى الحافظة!');
            });
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    try {
        // التحقق من وجود العناصر الأساسية
        const requiredElements = [
            'start-game', 'submit-answer', 'user-answer',
            'hint-btn', 'skip-btn', 'solution-btn',
            'play-again', 'share-results', 'modal'
        ];

        let missingElements = [];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                missingElements.push(id);
            }
        });

        if (missingElements.length > 0) {
            console.error('العناصر المفقودة:', missingElements);
            alert('خطأ: بعض عناصر الصفحة مفقودة. تأكد من تحميل الصفحة بشكل صحيح.');
            return;
        }

        // التحقق من وجود دوال الألغاز
        if (typeof getAllPuzzles === 'undefined') {
            console.error('ملف puzzles.js غير محمل');
            alert('خطأ: ملف الألغاز غير محمل. تأكد من وجود ملف puzzles.js');
            return;
        }

        // تشغيل اللعبة
        new CodePuzzleGame();
        console.log('✅ تم تحميل اللعبة بنجاح');

    } catch (error) {
        console.error('خطأ في تشغيل اللعبة:', error);
        alert('حدث خطأ في تشغيل اللعبة. تحقق من وحدة التحكم للمزيد من التفاصيل.');
    }
});
