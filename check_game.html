<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص اللعبة - Code Puzzle</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        .check-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
        .check-item.success {
            background: #f0fff4;
            border-color: #9ae6b4;
        }
        .check-item.error {
            background: #fff5f5;
            border-color: #feb2b2;
        }
        .check-item.loading {
            background: #ebf8ff;
            border-color: #90cdf4;
        }
        .icon {
            font-size: 1.5rem;
            margin-left: 15px;
            min-width: 30px;
        }
        .details {
            flex: 1;
        }
        .details h3 {
            margin: 0 0 5px 0;
            color: #4a5568;
        }
        .details p {
            margin: 0;
            color: #718096;
            font-size: 0.9rem;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص مكونات اللعبة</h1>
            <p>التحقق من جميع ملفات ومكونات Code Puzzle Game</p>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar"></div>
        </div>

        <div id="checks-container">
            <!-- سيتم إضافة نتائج الفحص هنا -->
        </div>

        <div class="actions">
            <button class="btn" onclick="runAllChecks()">🔄 إعادة الفحص</button>
            <a href="start.html" class="btn success">🚀 تشغيل اللعبة</a>
            <a href="index.html" class="btn">🎮 اللعبة مباشرة</a>
        </div>
    </div>

    <script>
        const checks = [
            {
                name: 'ملف HTML الرئيسي',
                file: 'index.html',
                description: 'الصفحة الرئيسية للعبة'
            },
            {
                name: 'ملف التصميم',
                file: 'styles.css',
                description: 'ملف CSS للتنسيق والألوان'
            },
            {
                name: 'ملف JavaScript الرئيسي',
                file: 'script.js',
                description: 'منطق اللعبة والتفاعل'
            },
            {
                name: 'ملف الألغاز',
                file: 'puzzles.js',
                description: 'قاعدة بيانات الألغاز البرمجية'
            },
            {
                name: 'صفحة التشغيل',
                file: 'start.html',
                description: 'صفحة تشغيل اللعبة بطرق مختلفة'
            },
            {
                name: 'صفحة الاختبار',
                file: 'test.html',
                description: 'صفحة اختبار مكونات اللعبة'
            },
            {
                name: 'ملف التشغيل التلقائي',
                file: 'start_game.bat',
                description: 'ملف تشغيل اللعبة على Windows'
            }
        ];

        let completedChecks = 0;

        async function checkFile(file) {
            try {
                const response = await fetch(file, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        function updateProgress() {
            const percentage = (completedChecks / checks.length) * 100;
            document.getElementById('progress-bar').style.width = percentage + '%';
        }

        function createCheckItem(check, status, details = '') {
            const item = document.createElement('div');
            item.className = `check-item ${status}`;
            
            let icon = '⏳';
            if (status === 'success') icon = '✅';
            else if (status === 'error') icon = '❌';
            
            item.innerHTML = `
                <div class="icon">${icon}</div>
                <div class="details">
                    <h3>${check.name}</h3>
                    <p>${check.description}</p>
                    ${details ? `<p><strong>${details}</strong></p>` : ''}
                </div>
            `;
            
            return item;
        }

        async function runSingleCheck(check, container) {
            // إضافة عنصر التحميل
            const loadingItem = createCheckItem(check, 'loading');
            container.appendChild(loadingItem);

            // فحص الملف
            const exists = await checkFile(check.file);
            
            // إزالة عنصر التحميل
            container.removeChild(loadingItem);
            
            // إضافة النتيجة
            const status = exists ? 'success' : 'error';
            const details = exists ? 'الملف موجود وجاهز' : 'الملف مفقود أو لا يمكن الوصول إليه';
            const resultItem = createCheckItem(check, status, details);
            container.appendChild(resultItem);
            
            completedChecks++;
            updateProgress();
            
            return exists;
        }

        async function runAllChecks() {
            const container = document.getElementById('checks-container');
            container.innerHTML = '';
            completedChecks = 0;
            updateProgress();

            let allPassed = true;

            // تشغيل جميع الفحوصات
            for (const check of checks) {
                const result = await runSingleCheck(check, container);
                if (!result) allPassed = false;
                
                // انتظار قصير بين الفحوصات للتأثير البصري
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // إضافة نتيجة إجمالية
            setTimeout(() => {
                const summaryItem = document.createElement('div');
                summaryItem.className = `check-item ${allPassed ? 'success' : 'error'}`;
                summaryItem.innerHTML = `
                    <div class="icon">${allPassed ? '🎉' : '⚠️'}</div>
                    <div class="details">
                        <h3>النتيجة الإجمالية</h3>
                        <p>${allPassed ? 'جميع الملفات موجودة! اللعبة جاهزة للتشغيل.' : 'بعض الملفات مفقودة. تحقق من الملفات المفقودة.'}</p>
                    </div>
                `;
                container.appendChild(summaryItem);
            }, 500);
        }

        // تشغيل الفحص عند تحميل الصفحة
        window.onload = function() {
            runAllChecks();
        };
    </script>
</body>
</html>
