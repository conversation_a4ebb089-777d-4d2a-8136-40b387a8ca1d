# دليل المطور - Code Puzzle Game

## 🏗️ هيكل المشروع

```
Game/
├── index.html              # الصفحة الرئيسية
├── styles.css              # ملف التنسيق الرئيسي
├── script.js               # منطق اللعبة الأساسي
├── puzzles.js              # قاعدة بيانات الألغاز
├── test.html               # صفحة اختبار اللعبة
├── README.md               # دليل المستخدم
└── DEVELOPER_GUIDE.md      # هذا الملف
```

## 🧩 إضافة ألغاز جديدة

### هيكل اللغز

```javascript
{
    title: "عنوان اللغز",
    description: "وصف مفصل للتحدي",
    code: `// الكود البرمجي هنا`,
    question: "السؤال المطلوب الإجابة عليه؟",
    answer: "الإجابة الصحيحة",
    hint: "تلميح مفيد للمساعدة",
    solution: "شرح مفصل للحل"
}
```

### إضافة لغز Java

```javascript
// في ملف puzzles.js تحت java.easy أو medium أو hard
{
    title: "حلقات Java",
    description: "احسب ناتج الحلقة التالية",
    code: `for(int i = 0; i < 3; i++) {
    System.out.println(i * 2);
}`,
    question: "ما هو آخر رقم سيتم طباعته؟",
    answer: "4",
    hint: "تتبع قيمة i في كل تكرار",
    solution: "i=0: 0*2=0, i=1: 1*2=2, i=2: 2*2=4"
}
```

### إضافة لغز HTML

```javascript
{
    title: "عناصر HTML",
    description: "عد العناصر في الكود التالي",
    code: `<div>
    <p>فقرة 1</p>
    <p>فقرة 2</p>
    <span>نص</span>
</div>`,
    question: "كم عدد عناصر p؟",
    answer: "2",
    hint: "ابحث عن جميع عناصر <p>",
    solution: "يوجد عنصران p في الكود"
}
```

### إضافة لغز CSS

```javascript
{
    title: "CSS Box Model",
    description: "احسب العرض الإجمالي",
    code: `.box {
    width: 100px;
    padding: 20px;
    border: 5px solid red;
}`,
    question: "ما هو العرض الإجمالي؟",
    answer: "150px",
    hint: "width + padding*2 + border*2",
    solution: "100 + 20*2 + 5*2 = 150px"
}
```

## 🎨 تخصيص التصميم

### تغيير الألوان الأساسية

في `styles.css`:

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --error-color: #f56565;
    --warning-color: #ed8936;
}
```

### إضافة تأثيرات جديدة

```css
/* تأثير جديد للأزرار */
.custom-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    transform: perspective(1px) translateZ(0);
}

.custom-button:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}
```

## ⚙️ إضافة مميزات جديدة

### 1. إضافة لغة برمجة جديدة

في `puzzles.js`:

```javascript
const puzzles = {
    // اللغات الموجودة...
    python: {
        easy: [
            {
                title: "متغيرات Python",
                description: "حدد نوع المتغير",
                code: `x = [1, 2, 3]
y = len(x)
print(type(y))`,
                question: "ما نوع المتغير y؟",
                answer: "int",
                hint: "len() ترجع عدد صحيح",
                solution: "len() ترجع int يمثل طول القائمة"
            }
        ],
        medium: [],
        hard: []
    }
};
```

في `index.html` أضف زر اللغة الجديدة:

```html
<button class="language-btn" data-language="python">
    <i class="fab fa-python"></i>
    <span>Python</span>
</button>
```

### 2. إضافة نظام مستويات

في `script.js`:

```javascript
class CodePuzzleGame {
    constructor() {
        // الخصائص الموجودة...
        this.playerLevel = 1;
        this.experiencePoints = 0;
    }

    calculateExperience(isCorrect) {
        if (isCorrect) {
            this.experiencePoints += 10;
            if (this.experiencePoints >= this.playerLevel * 100) {
                this.levelUp();
            }
        }
    }

    levelUp() {
        this.playerLevel++;
        this.showFeedback(`🎉 مستوى جديد! أنت الآن في المستوى ${this.playerLevel}`, 'success');
    }
}
```

### 3. إضافة نظام إنجازات

```javascript
const achievements = {
    'first_correct': {
        name: 'البداية الصحيحة',
        description: 'أجب على أول سؤال بشكل صحيح',
        icon: '🎯'
    },
    'speed_demon': {
        name: 'شيطان السرعة',
        description: 'أجب على 5 أسئلة في أقل من دقيقة',
        icon: '⚡'
    },
    'perfectionist': {
        name: 'المثالي',
        description: 'احصل على 100% في أي مستوى',
        icon: '💎'
    }
};
```

## 🔧 تحسين الأداء

### 1. تحسين تحميل الصور والخطوط

```html
<!-- في index.html -->
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" as="style">
<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">
```

### 2. تحسين JavaScript

```javascript
// استخدام debouncing للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تطبيق debouncing على البحث
const debouncedSearch = debounce(searchPuzzles, 300);
```

## 🧪 اختبار اللعبة

### اختبار الوحدات

```javascript
// في ملف test.js
function testPuzzleValidation() {
    const game = new CodePuzzleGame();
    
    // اختبار التحقق من الإجابات
    assert(game.checkAnswer("5", "5") === true);
    assert(game.checkAnswer("  5  ", "5") === true);
    assert(game.checkAnswer("Five", "5") === false);
    
    console.log("✅ جميع اختبارات التحقق من الإجابات نجحت");
}

function testScoreCalculation() {
    const game = new CodePuzzleGame();
    game.selectedDifficulty = 'hard';
    
    const score = game.calculateScore();
    assert(score === 200);
    
    console.log("✅ اختبار حساب النقاط نجح");
}
```

### اختبار التكامل

```javascript
// اختبار تدفق اللعبة الكامل
async function testGameFlow() {
    const game = new CodePuzzleGame();
    
    // اختيار الصعوبة واللغة
    game.selectDifficulty('easy');
    game.selectLanguage('java');
    
    // بدء اللعبة
    game.startGame();
    
    // محاكاة إجابة صحيحة
    document.getElementById('user-answer').value = 'correct answer';
    game.submitAnswer();
    
    // التحقق من النتائج
    assert(game.score > 0);
    assert(game.correctAnswers === 1);
    
    console.log("✅ اختبار تدفق اللعبة نجح");
}
```

## 📱 جعل اللعبة متجاوبة أكثر

### إضافة استعلامات وسائط جديدة

```css
/* للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .puzzle-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .difficulty-buttons,
    .language-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .code-container {
        font-size: 0.8rem;
        padding: 15px;
    }
}

/* للأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) {
    .container {
        padding: 30px;
    }
    
    .puzzle-container {
        max-width: 90%;
    }
}
```

## 🌐 إضافة دعم لغات متعددة

### هيكل الترجمة

```javascript
const translations = {
    ar: {
        welcome: "مرحباً بك في تحدي البرمجة!",
        start_game: "ابدأ اللعبة",
        score: "النقاط",
        time: "الوقت"
    },
    en: {
        welcome: "Welcome to Code Puzzle!",
        start_game: "Start Game",
        score: "Score",
        time: "Time"
    }
};

function translate(key, lang = 'ar') {
    return translations[lang][key] || key;
}
```

## 🚀 نشر اللعبة

### 1. GitHub Pages

```bash
# إنشاء repository جديد
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/username/code-puzzle.git
git push -u origin main

# تفعيل GitHub Pages من الإعدادات
```

### 2. Netlify

```bash
# رفع الملفات مباشرة أو ربط GitHub repository
# الموقع سيكون متاح على: https://app-name.netlify.app
```

### 3. Vercel

```bash
npm i -g vercel
vercel --prod
```

## 📊 إضافة تحليلات

### Google Analytics

```html
<!-- في index.html -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### تتبع الأحداث

```javascript
// في script.js
function trackEvent(action, category, label) {
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: category,
            event_label: label
        });
    }
}

// استخدام التتبع
submitAnswer() {
    // الكود الموجود...
    trackEvent('answer_submitted', 'gameplay', this.selectedLanguage);
}
```

---

**نصائح للتطوير:**

1. **اختبر دائماً** التغييرات على متصفحات مختلفة
2. **احتفظ بنسخ احتياطية** من الملفات المهمة
3. **اكتب تعليقات واضحة** في الكود
4. **استخدم أدوات التطوير** في المتصفح للتصحيح
5. **اطلب تعليقات المستخدمين** لتحسين اللعبة
