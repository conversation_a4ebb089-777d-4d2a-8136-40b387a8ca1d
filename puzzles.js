// Code Puzzles Database
const puzzles = {
    java: {
        easy: [
            {
                title: "متغيرات Java الأساسية",
                description: "في هذا التحدي، عليك فهم كيفية عمل المتغيرات في Java وتحديد القيمة النهائية.",
                code: `public class Variables {
    public static void main(String[] args) {
        int x = 5;
        int y = 10;
        x = x + y;
        y = x - y;
        x = x - y;
        System.out.println("x = " + x + ", y = " + y);
    }
}`,
                question: "ما هي قيمة x و y في النهاية؟ (مثال: x=5, y=10)",
                answer: "x=10, y=5",
                hint: "تتبع قيم المتغيرات خطوة بخطوة. هذا مثال على تبديل قيم المتغيرات.",
                solution: "القيم النهائية هي x=10 و y=5. هذا الكود يقوم بتبديل قيم المتغيرين باستخدام العمليات الحسابية."
            },
            {
                title: "حلقة For البسيطة",
                description: "احسب ناتج تنفيذ حلقة for التالية.",
                code: `public class Loop {
    public static void main(String[] args) {
        int sum = 0;
        for(int i = 1; i <= 5; i++) {
            sum += i;
        }
        System.out.println(sum);
    }
}`,
                question: "ما هي قيمة sum في النهاية؟",
                answer: "15",
                hint: "احسب مجموع الأرقام من 1 إلى 5: 1+2+3+4+5",
                solution: "sum = 1+2+3+4+5 = 15"
            },
            {
                title: "شروط if-else",
                description: "حدد ما سيتم طباعته من الكود التالي.",
                code: `public class Condition {
    public static void main(String[] args) {
        int age = 18;
        if(age >= 18) {
            System.out.println("بالغ");
        } else {
            System.out.println("قاصر");
        }
    }
}`,
                question: "ما الذي سيتم طباعته؟",
                answer: "بالغ",
                hint: "تحقق من الشرط: هل 18 >= 18؟",
                solution: "سيتم طباعة 'بالغ' لأن 18 >= 18 شرط صحيح."
            }
        ],
        medium: [
            {
                title: "مصفوفات Java",
                description: "احسب ناتج العمليات على المصفوفة التالية.",
                code: `public class Arrays {
    public static void main(String[] args) {
        int[] arr = {2, 4, 6, 8, 10};
        int result = 0;
        for(int i = 0; i < arr.length; i++) {
            if(arr[i] % 4 == 0) {
                result += arr[i];
            }
        }
        System.out.println(result);
    }
}`,
                question: "ما هي قيمة result؟",
                answer: "12",
                hint: "ابحث عن الأرقام القابلة للقسمة على 4 في المصفوفة واجمعها.",
                solution: "الأرقام القابلة للقسمة على 4 هي: 4 و 8. المجموع = 4 + 8 = 12"
            },
            {
                title: "دوال Java",
                description: "حدد ناتج استدعاء الدالة التالية.",
                code: `public class Methods {
    public static int calculate(int a, int b) {
        return a * b + a;
    }
    
    public static void main(String[] args) {
        int result = calculate(3, 4);
        System.out.println(result);
    }
}`,
                question: "ما هي قيمة result؟",
                answer: "15",
                hint: "احسب: a * b + a حيث a=3 و b=4",
                solution: "result = 3 * 4 + 3 = 12 + 3 = 15"
            }
        ],
        hard: [
            {
                title: "Recursion في Java",
                description: "احسب ناتج الدالة التكرارية التالية.",
                code: `public class Recursion {
    public static int factorial(int n) {
        if(n <= 1) {
            return 1;
        }
        return n * factorial(n - 1);
    }

    public static void main(String[] args) {
        System.out.println(factorial(4));
    }
}`,
                question: "ما هو ناتج factorial(4)؟",
                answer: "24",
                hint: "factorial(4) = 4 * 3 * 2 * 1",
                solution: "factorial(4) = 4 * factorial(3) = 4 * 3 * 2 * 1 = 24"
            },
            {
                title: "OOP في Java",
                description: "حدد ناتج تنفيذ الكود التالي.",
                code: `class Animal {
    String name = "حيوان";
    void sound() {
        System.out.println("صوت");
    }
}

class Cat extends Animal {
    String name = "قطة";
    void sound() {
        System.out.println("مواء");
    }
}

public class Main {
    public static void main(String[] args) {
        Animal a = new Cat();
        System.out.println(a.name);
        a.sound();
    }
}`,
                question: "ما الذي سيتم طباعته؟ (سطر واحد، مفصول بفاصلة)",
                answer: "حيوان, مواء",
                hint: "في Java، المتغيرات تتبع نوع المرجع، والدوال تتبع نوع الكائن الفعلي.",
                solution: "سيتم طباعة 'حيوان' ثم 'مواء'. المتغير name يتبع نوع المرجع (Animal) بينما الدالة sound() تتبع نوع الكائن الفعلي (Cat)."
            }
        ]
    },
    html: {
        easy: [
            {
                title: "عناصر HTML الأساسية",
                description: "حدد عدد عناصر p في الكود التالي.",
                code: `<!DOCTYPE html>
<html>
<head>
    <title>صفحة تجريبية</title>
</head>
<body>
    <h1>العنوان الرئيسي</h1>
    <p>الفقرة الأولى</p>
    <div>
        <p>الفقرة الثانية</p>
        <span>نص عادي</span>
        <p>الفقرة الثالثة</p>
    </div>
</body>
</html>`,
                question: "كم عدد عناصر <p> في الكود؟",
                answer: "3",
                hint: "عد جميع عناصر <p> في الكود، بما في ذلك التي داخل div.",
                solution: "يوجد 3 عناصر <p>: واحد خارج div واثنان داخل div."
            },
            {
                title: "خصائص HTML",
                description: "حدد قيمة خاصية class للعنصر الثاني.",
                code: `<div class="container">
    <p class="text-primary">النص الأول</p>
    <p class="text-secondary">النص الثاني</p>
    <p class="text-primary">النص الثالث</p>
</div>`,
                question: "ما هي قيمة class للعنصر p الثاني؟",
                answer: "text-secondary",
                hint: "انظر إلى العنصر p الثاني في الترتيب.",
                solution: "العنصر p الثاني له class='text-secondary'"
            },
            {
                title: "قوائم HTML",
                description: "حدد نوع القائمة المستخدمة في الكود التالي.",
                code: `<ul>
    <li>العنصر الأول</li>
    <li>العنصر الثاني
        <ol>
            <li>عنصر فرعي 1</li>
            <li>عنصر فرعي 2</li>
        </ol>
    </li>
    <li>العنصر الثالث</li>
</ul>`,
                question: "كم عدد عناصر القائمة الرئيسية (li المباشرة تحت ul)؟",
                answer: "3",
                hint: "عد فقط عناصر li التي تحت ul مباشرة، وليس التي تحت ol.",
                solution: "يوجد 3 عناصر li مباشرة تحت ul الرئيسية."
            }
        ],
        medium: [
            {
                title: "جداول HTML",
                description: "احسب عدد الخلايا في الجدول التالي.",
                code: `<table>
    <tr>
        <th>الاسم</th>
        <th>العمر</th>
        <th>المدينة</th>
    </tr>
    <tr>
        <td>أحمد</td>
        <td>25</td>
        <td>الرياض</td>
    </tr>
    <tr>
        <td>فاطمة</td>
        <td>30</td>
        <td>جدة</td>
    </tr>
</table>`,
                question: "كم عدد خلايا البيانات (td) في الجدول؟",
                answer: "6",
                hint: "عد فقط عناصر <td>، وليس <th>.",
                solution: "يوجد 6 خلايا بيانات (td): 3 في كل صف من صفي البيانات."
            },
            {
                title: "خصائص HTML المتقدمة",
                description: "حدد قيمة خاصية معينة في الكود التالي.",
                code: `<form action="/submit" method="post">
    <input type="text" name="username" required>
    <input type="password" name="password" minlength="8">
    <input type="email" name="email" placeholder="أدخل بريدك الإلكتروني">
    <button type="submit">إرسال</button>
</form>`,
                question: "ما هي قيمة خاصية method في النموذج؟",
                answer: "post",
                hint: "انظر إلى عنصر form وخاصية method.",
                solution: "قيمة خاصية method هي 'post'."
            }
        ],
        hard: [
            {
                title: "نماذج HTML المعقدة",
                description: "حدد عدد عناصر الإدخال في النموذج.",
                code: `<form>
    <fieldset>
        <legend>معلومات شخصية</legend>
        <input type="text" name="name">
        <input type="email" name="email">
        <select name="city">
            <option>الرياض</option>
            <option>جدة</option>
        </select>
    </fieldset>
    <fieldset>
        <legend>تفضيلات</legend>
        <input type="checkbox" name="newsletter">
        <input type="radio" name="gender" value="male">
        <input type="radio" name="gender" value="female">
        <textarea name="comments"></textarea>
    </fieldset>
    <input type="submit" value="إرسال">
</form>`,
                question: "كم عدد عناصر input في النموذج؟",
                answer: "6",
                hint: "عد جميع عناصر <input> بجميع أنواعها.",
                solution: "يوجد 6 عناصر input: text, email, checkbox, radio (2), submit"
            },
            {
                title: "HTML5 Semantic Elements",
                description: "حدد العناصر الدلالية في الكود التالي.",
                code: `<!DOCTYPE html>
<html>
<head>
    <title>موقع إخباري</title>
</head>
<body>
    <header>
        <nav>
            <ul>
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#news">الأخبار</a></li>
            </ul>
        </nav>
    </header>
    <main>
        <article>
            <h1>خبر مهم</h1>
            <p>محتوى الخبر...</p>
        </article>
        <aside>
            <h2>أخبار ذات صلة</h2>
        </aside>
    </main>
    <footer>
        <p>جميع الحقوق محفوظة</p>
    </footer>
</body>
</html>`,
                question: "كم عدد العناصر الدلالية HTML5 المستخدمة؟",
                answer: "6",
                hint: "العناصر الدلالية هي: header, nav, main, article, aside, footer",
                solution: "العناصر الدلالية المستخدمة هي: header, nav, main, article, aside, footer = 6 عناصر"
            }
        ]
    },
    css: {
        easy: [
            {
                title: "CSS Selectors الأساسية",
                description: "حدد لون النص للعنصر p ذو class='highlight'.",
                code: `p {
    color: blue;
}

.highlight {
    color: red;
}

#special {
    color: green;
}`,
                question: "ما لون النص لعنصر <p class='highlight'>؟",
                answer: "red",
                hint: "class selector له أولوية أعلى من element selector.",
                solution: "اللون سيكون أحمر (red) لأن .highlight له أولوية أعلى من p."
            },
            {
                title: "CSS Box Model",
                description: "احسب العرض الإجمالي للعنصر.",
                code: `.box {
    width: 200px;
    padding: 10px;
    border: 5px solid black;
    margin: 20px;
}`,
                question: "ما هو العرض الإجمالي للعنصر (بما في ذلك padding و border)؟",
                answer: "230px",
                hint: "العرض الإجمالي = width + padding-left + padding-right + border-left + border-right",
                solution: "العرض الإجمالي = 200 + 10 + 10 + 5 + 5 = 230px"
            },
            {
                title: "CSS Colors والوحدات",
                description: "حدد اللون النهائي للعنصر.",
                code: `.parent {
    color: blue;
}

.child {
    color: inherit;
    background: rgba(255, 0, 0, 0.5);
}`,
                question: "ما لون النص في العنصر ذو class='child' داخل parent؟",
                answer: "blue",
                hint: "inherit تعني وراثة القيمة من العنصر الأب.",
                solution: "لون النص سيكون أزرق (blue) لأن inherit ترث اللون من parent."
            }
        ],
        medium: [
            {
                title: "CSS Flexbox",
                description: "حدد ترتيب العناصر في flexbox.",
                code: `.container {
    display: flex;
    flex-direction: row-reverse;
}

.item1 { order: 2; }
.item2 { order: 1; }
.item3 { order: 3; }`,
                question: "ما هو ترتيب ظهور العناصر؟ (مثال: item2, item1, item3)",
                answer: "item2, item1, item3",
                hint: "flex-direction: row-reverse يعكس الاتجاه، و order يحدد الترتيب.",
                solution: "بسبب row-reverse والـ order، الترتيب سيكون: item2, item1, item3"
            },
            {
                title: "CSS Positioning",
                description: "حدد موقع العنصر النهائي.",
                code: `.container {
    position: relative;
    width: 300px;
    height: 200px;
}

.box {
    position: absolute;
    top: 50px;
    right: 30px;
    width: 100px;
    height: 50px;
}`,
                question: "كم بكسل من اليسار سيكون العنصر box؟",
                answer: "170px",
                hint: "احسب: عرض الحاوية - right - عرض العنصر",
                solution: "المسافة من اليسار = 300 - 30 - 100 = 170px"
            }
        ],
        hard: [
            {
                title: "CSS Grid المتقدم",
                description: "حدد موقع العنصر في CSS Grid.",
                code: `.grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 100px);
}

.item {
    grid-column: 2 / 4;
    grid-row: 1 / 3;
}`,
                question: "كم عدد الخلايا التي يشغلها العنصر؟",
                answer: "4",
                hint: "احسب عدد الأعمدة × عدد الصفوف التي يشغلها العنصر.",
                solution: "العنصر يشغل عمودين (2 إلى 4) وصفين (1 إلى 3) = 2 × 2 = 4 خلايا"
            },
            {
                title: "CSS Animations",
                description: "حدد مدة الحركة الإجمالية.",
                code: `@keyframes slideIn {
    0% { transform: translateX(-100px); }
    50% { transform: translateX(0); }
    100% { transform: translateX(50px); }
}

.animated {
    animation: slideIn 2s ease-in-out 3;
}`,
                question: "كم ثانية ستستغرق الحركة الكاملة؟",
                answer: "6",
                hint: "مدة الحركة × عدد التكرارات = 2s × 3",
                solution: "مدة الحركة الكاملة = 2 ثانية × 3 تكرارات = 6 ثواني"
            },
            {
                title: "CSS Specificity",
                description: "حدد اللون النهائي للعنصر.",
                code: `#container .box { color: red; }
.special { color: blue !important; }
div.box { color: green; }
.box { color: yellow; }

<!-- HTML -->
<div id="container">
    <div class="box special">النص</div>
</div>`,
                question: "ما لون النص النهائي؟",
                answer: "blue",
                hint: "!important له أولوية عالية جداً.",
                solution: "اللون سيكون أزرق (blue) لأن !important له أولوية أعلى من جميع القواعد الأخرى."
            }
        ]
    }
};

// Utility functions for puzzles
function getPuzzlesByLanguageAndDifficulty(language, difficulty) {
    return puzzles[language] && puzzles[language][difficulty] ? puzzles[language][difficulty] : [];
}

function getRandomPuzzle(language, difficulty) {
    const puzzleList = getPuzzlesByLanguageAndDifficulty(language, difficulty);
    if (puzzleList.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * puzzleList.length);
    return puzzleList[randomIndex];
}

function getAllPuzzles(language, difficulty) {
    return getPuzzlesByLanguageAndDifficulty(language, difficulty);
}
