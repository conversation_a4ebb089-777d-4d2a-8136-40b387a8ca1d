@echo off
chcp 65001 >nul
title Code Puzzle Game Launcher
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎮 Code Puzzle Game 🎮                    ║
echo ║                   تشغيل اللعبة بسهولة                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1] تشغيل اللعبة مباشرة (بدون خادم)
echo [2] تشغيل اللعبة مع خادم محلي (مستحسن)
echo [3] فتح صفحة الاختبار
echo [4] فتح مجلد اللعبة
echo [5] خروج
echo.

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" goto direct
if "%choice%"=="2" goto server
if "%choice%"=="3" goto test
if "%choice%"=="4" goto folder
if "%choice%"=="5" goto exit
goto invalid

:direct
echo.
echo 🚀 جاري تشغيل اللعبة مباشرة...
start "" "index.html"
echo ✅ تم فتح اللعبة في المتصفح الافتراضي
echo.
pause
goto menu

:server
echo.
echo 🖥️ جاري تشغيل الخادم المحلي...
echo 📡 الخادم سيعمل على: http://localhost:8000
echo 🔄 لإيقاف الخادم اضغط Ctrl+C
echo.
echo ⏳ انتظر قليلاً ثم سيتم فتح اللعبة تلقائياً...
echo.

rem تشغيل الخادم في الخلفية
start /min python -m http.server 8000

rem انتظار 3 ثوان ثم فتح المتصفح
timeout /t 3 /nobreak >nul
start "" "http://localhost:8000"

echo ✅ تم تشغيل اللعبة على الخادم المحلي
echo.
echo 📝 ملاحظة: لا تغلق هذه النافذة أثناء اللعب
echo 🛑 لإيقاف الخادم اضغط Ctrl+C
echo.
pause
goto menu

:test
echo.
echo 🧪 جاري فتح صفحة الاختبار...
start "" "test.html"
echo ✅ تم فتح صفحة الاختبار
echo.
pause
goto menu

:folder
echo.
echo 📁 جاري فتح مجلد اللعبة...
start "" "%cd%"
echo ✅ تم فتح مجلد اللعبة
echo.
pause
goto menu

:invalid
echo.
echo ❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 5
echo.
pause
goto menu

:menu
cls
goto start

:exit
echo.
echo 👋 شكراً لاستخدام Code Puzzle Game!
echo 🎮 نراك قريباً...
echo.
timeout /t 2 /nobreak >nul
exit

:start
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎮 Code Puzzle Game 🎮                    ║
echo ║                   تشغيل اللعبة بسهولة                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1] تشغيل اللعبة مباشرة (بدون خادم)
echo [2] تشغيل اللعبة مع خادم محلي (مستحسن)
echo [3] فتح صفحة الاختبار
echo [4] فتح مجلد اللعبة
echo [5] خروج
echo.

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" goto direct
if "%choice%"=="2" goto server
if "%choice%"=="3" goto test
if "%choice%"=="4" goto folder
if "%choice%"=="5" goto exit
goto invalid
