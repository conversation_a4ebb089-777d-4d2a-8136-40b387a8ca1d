<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Puzzle - تشغيل سريع</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .launcher {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .launcher h1 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 2.5rem;
        }
        .launcher p {
            color: #718096;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        .launch-btn {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 1.3rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .launch-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .launch-btn.server {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .launch-btn.direct {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }
        .instructions {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: right;
        }
        .instructions h3 {
            color: #4a5568;
            margin-bottom: 15px;
        }
        .instructions ol {
            color: #718096;
            text-align: right;
        }
        .instructions li {
            margin-bottom: 8px;
        }
        .error-msg {
            background: #fed7d7;
            color: #742a2a;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }
        .success-msg {
            background: #c6f6d5;
            color: #22543d;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="launcher">
        <h1>🎮 Code Puzzle</h1>
        <p>اختر طريقة تشغيل اللعبة:</p>
        
        <button class="launch-btn direct" onclick="openDirect()">
            🚀 تشغيل مباشر
        </button>
        
        <button class="launch-btn server" onclick="startServer()">
            🖥️ تشغيل بخادم محلي
        </button>
        
        <a href="test.html" class="launch-btn" style="background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);">
            🧪 صفحة الاختبار
        </a>
        
        <div class="success-msg" id="success-msg">
            ✅ تم تشغيل اللعبة بنجاح!
        </div>
        
        <div class="error-msg" id="error-msg">
            ❌ حدث خطأ في التشغيل. جرب الطريقة الأخرى.
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات التشغيل:</h3>
            <ol>
                <li><strong>التشغيل المباشر:</strong> يفتح اللعبة مباشرة في المتصفح</li>
                <li><strong>التشغيل بخادم:</strong> يشغل خادم محلي ثم يفتح اللعبة</li>
                <li><strong>صفحة الاختبار:</strong> لاختبار جميع مكونات اللعبة</li>
            </ol>
            
            <h3>🔧 في حالة وجود مشاكل:</h3>
            <ol>
                <li>تأكد من وجود جميع الملفات في نفس المجلد</li>
                <li>جرب تشغيل اللعبة بطريقة مختلفة</li>
                <li>تأكد من تفعيل JavaScript في المتصفح</li>
                <li>استخدم متصفح حديث (Chrome, Firefox, Edge)</li>
            </ol>
        </div>
    </div>

    <script>
        function openDirect() {
            try {
                // فتح اللعبة مباشرة
                window.open('index.html', '_blank');
                showMessage('success', 'تم فتح اللعبة في نافذة جديدة!');
            } catch (error) {
                showMessage('error', 'فشل في فتح اللعبة مباشرة. جرب التشغيل بخادم محلي.');
            }
        }
        
        function startServer() {
            showMessage('success', 'جاري تشغيل الخادم المحلي...');
            
            // محاولة فتح اللعبة على الخادم المحلي
            setTimeout(() => {
                try {
                    window.open('http://localhost:8000', '_blank');
                    showMessage('success', 'تم فتح اللعبة على الخادم المحلي!');
                } catch (error) {
                    showMessage('error', 'تأكد من تشغيل الخادم أولاً باستخدام: python -m http.server 8000');
                }
            }, 1000);
        }
        
        function showMessage(type, message) {
            // إخفاء جميع الرسائل
            document.getElementById('success-msg').style.display = 'none';
            document.getElementById('error-msg').style.display = 'none';
            
            // عرض الرسالة المناسبة
            const msgElement = document.getElementById(type + '-msg');
            msgElement.textContent = message;
            msgElement.style.display = 'block';
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                msgElement.style.display = 'none';
            }, 5000);
        }
        
        // فحص حالة الملفات عند تحميل الصفحة
        window.onload = function() {
            // فحص وجود الملفات الأساسية
            const files = ['index.html', 'styles.css', 'script.js', 'puzzles.js'];
            let allFilesExist = true;
            
            files.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        if (!response.ok) {
                            allFilesExist = false;
                        }
                    })
                    .catch(() => {
                        allFilesExist = false;
                    });
            });
            
            setTimeout(() => {
                if (!allFilesExist) {
                    showMessage('error', 'تأكد من وجود جميع ملفات اللعبة في نفس المجلد');
                }
            }, 1000);
        };
    </script>
</body>
</html>
