/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Stats Bar */
.stats-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stat {
    background: rgba(255,255,255,0.2);
    padding: 15px 25px;
    border-radius: 15px;
    color: white;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.stat i {
    margin-left: 10px;
    color: #ffd700;
}

/* Game Area */
.game-area {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    min-height: 600px;
}

.screen {
    display: none;
    padding: 40px;
}

.screen.active {
    display: block;
}

/* Welcome Screen */
.welcome-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.welcome-content h2 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.welcome-content p {
    font-size: 1.2rem;
    color: #718096;
    margin-bottom: 40px;
}

.difficulty-selection, .language-selection {
    margin-bottom: 40px;
}

.difficulty-selection h3, .language-selection h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.difficulty-buttons, .language-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.difficulty-btn, .language-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px 30px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 1rem;
    min-width: 120px;
}

.difficulty-btn:hover, .language-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.difficulty-btn.selected, .language-btn.selected {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    transform: scale(1.05);
}

.difficulty-btn i, .language-btn i {
    display: block;
    font-size: 2rem;
    margin-bottom: 10px;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 15px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.start-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.start-btn:not(:disabled):hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.start-btn i {
    margin-left: 10px;
}

/* Best Scores Section */
.best-scores {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px solid #e9ecef;
}

.best-scores h3 {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 15px;
    text-align: center;
}

.scores-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.score-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease;
}

.score-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.score-item .language {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.score-item .difficulty {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 10px;
}

.score-item .score {
    font-size: 1.5rem;
    font-weight: 700;
    color: #48bb78;
}

.score-item .score::after {
    content: " نقطة";
    font-size: 0.8rem;
    color: #718096;
}

/* Game Screen */
.puzzle-container {
    max-width: 800px;
    margin: 0 auto;
}

.puzzle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.puzzle-header h3 {
    font-size: 2rem;
    color: #4a5568;
}

.puzzle-info {
    display: flex;
    gap: 15px;
}

.puzzle-language, .puzzle-difficulty {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.puzzle-language {
    background: #e6fffa;
    color: #234e52;
}

.puzzle-difficulty {
    background: #fef5e7;
    color: #744210;
}

.puzzle-content > div {
    margin-bottom: 30px;
}

.puzzle-content h4 {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 15px;
    border-right: 4px solid #667eea;
    padding-right: 15px;
}

.puzzle-description p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #718096;
}

.code-container {
    background: #1a202c;
    border-radius: 10px;
    padding: 20px;
    overflow-x: auto;
    border: 1px solid #4a5568;
    position: relative;
}

.code-container::before {
    content: "💻 Code";
    position: absolute;
    top: -10px;
    right: 15px;
    background: #1a202c;
    color: #81c784;
    padding: 0 10px;
    font-size: 0.8rem;
    font-weight: 600;
}

.code-container pre {
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

/* Syntax highlighting for different languages */
.code-container pre code {
    color: #e2e8f0;
}

.code-container pre .keyword {
    color: #81c784;
    font-weight: bold;
}

.code-container pre .string {
    color: #ffb74d;
}

.code-container pre .comment {
    color: #757575;
    font-style: italic;
}

.code-container pre .number {
    color: #64b5f6;
}

.question-section p {
    font-size: 1.2rem;
    color: #4a5568;
    font-weight: 600;
}

.answer-input {
    display: flex;
    gap: 15px;
    align-items: center;
}

.answer-input input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1.1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.answer-input input:focus {
    outline: none;
    border-color: #667eea;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 10px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.puzzle-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 12px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-2px);
}

.action-btn i {
    margin-left: 8px;
}

/* Feedback Area */
.feedback-area {
    margin-top: 30px;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-weight: 600;
    display: none;
}

.feedback-area.success {
    background: #f0fff4;
    color: #22543d;
    border: 2px solid #9ae6b4;
    display: block;
    animation: slideInUp 0.5s ease-out;
}

.feedback-area.success::before {
    content: "✅ ";
    font-size: 1.2em;
}

.feedback-area.error {
    background: #fff5f5;
    color: #742a2a;
    border: 2px solid #feb2b2;
    display: block;
    animation: slideInUp 0.5s ease-out;
}

.feedback-area.error::before {
    content: "❌ ";
    font-size: 1.2em;
}

.feedback-area.info {
    background: #ebf8ff;
    color: #2a4365;
    border: 2px solid #90cdf4;
    display: block;
    animation: slideInUp 0.5s ease-out;
}

.feedback-area.info::before {
    content: "ℹ️ ";
    font-size: 1.2em;
}

/* Animation for feedback */
@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Pulse animation for buttons */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.submit-btn:hover {
    animation: pulse 0.6s infinite;
}

/* Results Screen */
.results-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.results-content h2 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 40px;
}

.final-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.stat-item {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    min-width: 150px;
}

.stat-item i {
    display: block;
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

.performance-rating {
    margin-bottom: 40px;
}

.performance-rating h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 20px;
}

.rating {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 15px;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 15px;
    left: 20px;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .stats-bar {
        gap: 15px;
    }
    
    .stat {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .screen {
        padding: 20px;
    }
    
    .puzzle-header {
        flex-direction: column;
        text-align: center;
    }
    
    .answer-input {
        flex-direction: column;
    }
    
    .answer-input input {
        margin-bottom: 15px;
    }
    
    .final-stats {
        flex-direction: column;
        gap: 15px;
    }
}
