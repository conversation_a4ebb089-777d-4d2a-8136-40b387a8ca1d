<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لعبة Code Puzzle</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار لعبة Code Puzzle</h1>
        
        <div class="test-item info">
            <h3>معلومات اللعبة:</h3>
            <ul>
                <li>✅ تم إنشاء جميع الملفات بنجاح</li>
                <li>✅ الخادم يعمل على المنفذ 8000</li>
                <li>✅ تم إضافة ألغاز لـ Java و HTML و CSS</li>
                <li>✅ تم إضافة نظام النقاط وحفظ أفضل النتائج</li>
                <li>✅ تم إضافة تأثيرات بصرية وتمييز الكود</li>
            </ul>
        </div>

        <div class="test-item success">
            <h3>المميزات المتاحة:</h3>
            <ul>
                <li>🎮 ثلاث لغات برمجة: Java, HTML, CSS</li>
                <li>📊 ثلاثة مستويات صعوبة: مبتدئ، متوسط، متقدم</li>
                <li>⏱️ مؤقت لتتبع الوقت</li>
                <li>💡 تلميحات مفيدة</li>
                <li>👁️ عرض الحلول التفصيلية</li>
                <li>🏆 نظام نقاط وتقييم بالنجوم</li>
                <li>💾 حفظ أفضل النتائج محلياً</li>
                <li>📱 تصميم متجاوب</li>
                <li>🎨 واجهة عربية جذابة</li>
            </ul>
        </div>

        <div class="test-item">
            <h3>اختبار سريع للألغاز:</h3>
            <div id="puzzle-test"></div>
            <button onclick="testPuzzles()">اختبار الألغاز</button>
            <button onclick="window.open('index.html', '_blank')">فتح اللعبة</button>
        </div>

        <div class="test-item">
            <h3>إرشادات الاختبار:</h3>
            <ol>
                <li>اضغط "فتح اللعبة" لتشغيل اللعبة</li>
                <li>اختر مستوى الصعوبة ولغة البرمجة</li>
                <li>اضغط "ابدأ اللعبة"</li>
                <li>جرب حل بعض الألغاز</li>
                <li>اختبر الأزرار: تلميح، تخطي، الحل</li>
                <li>تحقق من النتائج النهائية</li>
            </ol>
        </div>
    </div>

    <script>
        function testPuzzles() {
            // Load puzzles data
            fetch('puzzles.js')
                .then(response => response.text())
                .then(data => {
                    // Execute the puzzles.js content
                    eval(data);
                    
                    const testDiv = document.getElementById('puzzle-test');
                    let html = '<h4>نتائج اختبار الألغاز:</h4>';
                    
                    const languages = ['java', 'html', 'css'];
                    const difficulties = ['easy', 'medium', 'hard'];
                    
                    languages.forEach(lang => {
                        difficulties.forEach(diff => {
                            const puzzleList = getPuzzlesByLanguageAndDifficulty(lang, diff);
                            const count = puzzleList.length;
                            const status = count > 0 ? 'success' : 'error';
                            html += `<div class="${status}">
                                ${lang.toUpperCase()} - ${diff}: ${count} ألغاز
                            </div>`;
                        });
                    });
                    
                    testDiv.innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('puzzle-test').innerHTML = 
                        '<div class="error">خطأ في تحميل الألغاز: ' + error + '</div>';
                });
        }

        // Auto-test on page load
        window.onload = function() {
            testPuzzles();
        };
    </script>
</body>
</html>
