# Code Puzzle Game PowerShell Launcher
# تشغيل لعبة Code Puzzle بسهولة

param(
    [string]$Mode = "menu"
)

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Show-Header {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    🎮 Code Puzzle Game 🎮                    ║" -ForegroundColor Cyan
    Write-Host "║                   تشغيل اللعبة بسهولة                      ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

function Show-Menu {
    Write-Host "[1] تشغيل اللعبة مباشرة (بدون خادم)" -ForegroundColor Green
    Write-Host "[2] تشغيل اللعبة مع خادم محلي (مستحسن)" -ForegroundColor Yellow
    Write-Host "[3] فتح صفحة الاختبار" -ForegroundColor Magenta
    Write-Host "[4] فتح مجلد اللعبة" -ForegroundColor Blue
    Write-Host "[5] فحص الملفات" -ForegroundColor Cyan
    Write-Host "[6] خروج" -ForegroundColor Red
    Write-Host ""
}

function Test-GameFiles {
    Write-Host "🔍 جاري فحص ملفات اللعبة..." -ForegroundColor Yellow
    
    $requiredFiles = @("index.html", "styles.css", "script.js", "puzzles.js")
    $missingFiles = @()
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Host "✅ $file موجود" -ForegroundColor Green
        } else {
            Write-Host "❌ $file مفقود" -ForegroundColor Red
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -eq 0) {
        Write-Host "🎉 جميع الملفات موجودة!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️  بعض الملفات مفقودة. تأكد من وجود جميع الملفات." -ForegroundColor Red
        return $false
    }
}

function Start-DirectMode {
    Write-Host "🚀 جاري تشغيل اللعبة مباشرة..." -ForegroundColor Green
    
    if (Test-Path "index.html") {
        Start-Process "index.html"
        Write-Host "✅ تم فتح اللعبة في المتصفح الافتراضي" -ForegroundColor Green
    } else {
        Write-Host "❌ ملف index.html غير موجود!" -ForegroundColor Red
    }
}

function Start-ServerMode {
    Write-Host "🖥️ جاري تشغيل الخادم المحلي..." -ForegroundColor Yellow
    Write-Host "📡 الخادم سيعمل على: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "🔄 لإيقاف الخادم اضغط Ctrl+C" -ForegroundColor Yellow
    Write-Host ""
    
    # فحص إذا كان Python متاحاً
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "🐍 Python متاح: $pythonVersion" -ForegroundColor Green
        
        # تشغيل الخادم
        Write-Host "⏳ انتظر قليلاً ثم سيتم فتح اللعبة تلقائياً..." -ForegroundColor Yellow
        
        # تشغيل الخادم في الخلفية
        $serverJob = Start-Job -ScriptBlock { python -m http.server 8000 }
        
        # انتظار 3 ثوان
        Start-Sleep -Seconds 3
        
        # فتح المتصفح
        Start-Process "http://localhost:8000"
        
        Write-Host "✅ تم تشغيل اللعبة على الخادم المحلي" -ForegroundColor Green
        Write-Host "📝 ملاحظة: الخادم يعمل في الخلفية" -ForegroundColor Cyan
        Write-Host "🛑 لإيقاف الخادم استخدم الخيار 6 من القائمة" -ForegroundColor Yellow
        
    } catch {
        Write-Host "❌ Python غير متاح! يرجى تثبيت Python أولاً." -ForegroundColor Red
        Write-Host "💡 أو استخدم التشغيل المباشر (الخيار 1)" -ForegroundColor Yellow
    }
}

function Open-TestPage {
    Write-Host "🧪 جاري فتح صفحة الاختبار..." -ForegroundColor Magenta
    
    if (Test-Path "test.html") {
        Start-Process "test.html"
        Write-Host "✅ تم فتح صفحة الاختبار" -ForegroundColor Green
    } else {
        Write-Host "❌ ملف test.html غير موجود!" -ForegroundColor Red
    }
}

function Open-GameFolder {
    Write-Host "📁 جاري فتح مجلد اللعبة..." -ForegroundColor Blue
    Start-Process "."
    Write-Host "✅ تم فتح مجلد اللعبة" -ForegroundColor Green
}

function Stop-AllServers {
    Write-Host "🛑 جاري إيقاف جميع الخوادم..." -ForegroundColor Red
    Get-Job | Stop-Job
    Get-Job | Remove-Job
    Write-Host "✅ تم إيقاف جميع الخوادم" -ForegroundColor Green
}

# البرنامج الرئيسي
if ($Mode -eq "direct") {
    Start-DirectMode
    exit
} elseif ($Mode -eq "server") {
    Start-ServerMode
    exit
} elseif ($Mode -eq "test") {
    Open-TestPage
    exit
}

# القائمة التفاعلية
do {
    Show-Header
    Show-Menu
    
    $choice = Read-Host "اختر رقم الخيار (1-6)"
    
    switch ($choice) {
        "1" { 
            Start-DirectMode
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
        }
        "2" { 
            Start-ServerMode
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
        }
        "3" { 
            Open-TestPage
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
        }
        "4" { 
            Open-GameFolder
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
        }
        "5" { 
            Test-GameFiles
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
        }
        "6" { 
            Stop-AllServers
            Write-Host "👋 شكراً لاستخدام Code Puzzle Game!" -ForegroundColor Green
            Write-Host "🎮 نراك قريباً..." -ForegroundColor Cyan
            Start-Sleep -Seconds 2
            exit
        }
        default { 
            Write-Host "❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 6" -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
} while ($true)
