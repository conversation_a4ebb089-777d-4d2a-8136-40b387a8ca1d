🎮 Code Puzzle Game - دليل التشغيل السريع
═══════════════════════════════════════════════════

🚀 طرق تشغيل اللعبة (اختر الأسهل لك):

┌─────────────────────────────────────────────────┐
│ 1️⃣ الطريقة الأسهل - ملف التشغيل التلقائي      │
└─────────────────────────────────────────────────┘
• انقر مرتين على ملف: start_game.bat
• اختر الخيار المناسب من القائمة
• ستفتح اللعبة تلقائياً!

┌─────────────────────────────────────────────────┐
│ 2️⃣ التشغيل المباشر                            │
└─────────────────────────────────────────────────┘
• انقر مرتين على ملف: start.html
• اضغط "تشغيل مباشر"
• أو انقر مرتين على: index.html

┌─────────────────────────────────────────────────┐
│ 3️⃣ التشغيل بخادم محلي (الأفضل)                │
└─────────────────────────────────────────────────┘
• افتح Command Prompt أو PowerShell
• اكتب: python -m http.server 8000
• افتح المتصفح على: http://localhost:8000

┌─────────────────────────────────────────────────┐
│ 4️⃣ PowerShell (للمتقدمين)                     │
└─────────────────────────────────────────────────┘
• انقر بالزر الأيمن على start_game.ps1
• اختر "Run with PowerShell"

═══════════════════════════════════════════════════

🔧 حل المشاكل الشائعة:

❌ المشكلة: اللعبة لا تعمل
✅ الحل:
   • تأكد من وجود جميع الملفات في نفس المجلد
   • استخدم متصفح حديث (Chrome, Firefox, Edge)
   • تأكد من تفعيل JavaScript

❌ المشكلة: الخادم لا يعمل
✅ الحل:
   • تأكد من تثبيت Python
   • جرب التشغيل المباشر بدلاً من ذلك

❌ المشكلة: الصفحة فارغة
✅ الحل:
   • تحقق من وحدة التحكم في المتصفح (F12)
   • تأكد من تحميل جميع الملفات

❌ المشكلة: الألغاز لا تظهر
✅ الحل:
   • تأكد من وجود ملف puzzles.js
   • أعد تحميل الصفحة (F5)

═══════════════════════════════════════════════════

📁 الملفات المطلوبة:
✅ index.html (الصفحة الرئيسية)
✅ styles.css (التصميم)
✅ script.js (منطق اللعبة)
✅ puzzles.js (الألغاز)
✅ start.html (صفحة التشغيل)
✅ start_game.bat (ملف التشغيل)

📁 الملفات الاختيارية:
• test.html (صفحة الاختبار)
• start_game.ps1 (PowerShell)
• README.md (دليل مفصل)

═══════════════════════════════════════════════════

🎯 كيفية اللعب:

1. اختر مستوى الصعوبة:
   • مبتدئ: للمبتدئين
   • متوسط: للمتوسطين
   • متقدم: للخبراء

2. اختر لغة البرمجة:
   • Java: لغة البرمجة الشهيرة
   • HTML: لغة تصميم المواقع
   • CSS: لغة تنسيق المواقع

3. ابدأ اللعبة واستمتع!

═══════════════════════════════════════════════════

💡 نصائح للحصول على أفضل تجربة:

• استخدم التشغيل بخادم محلي للحصول على أفضل أداء
• جرب جميع مستويات الصعوبة
• استخدم التلميحات عند الحاجة
• شارك نتائجك مع الأصدقاء
• تحدى نفسك لتحسين النتائج

═══════════════════════════════════════════════════

📞 الدعم:
إذا واجهت أي مشاكل:
• تحقق من ملف README.md للمزيد من التفاصيل
• راجع ملف DEVELOPER_GUIDE.md للمطورين
• تأكد من استخدام متصفح حديث

═══════════════════════════════════════════════════

🎉 استمتع باللعبة وحظاً موفقاً! 🎉
