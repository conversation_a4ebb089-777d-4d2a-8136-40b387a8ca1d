<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ملف الألغاز</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار ملف الألغاز</h1>
        <div id="results"></div>
    </div>

    <script src="puzzles.js"></script>
    <script>
        function runTests() {
            const results = document.getElementById('results');
            let allTestsPassed = true;

            function addResult(message, type = 'info') {
                const div = document.createElement('div');
                div.className = `result ${type}`;
                div.innerHTML = message;
                results.appendChild(div);
                if (type === 'error') allTestsPassed = false;
            }

            try {
                // اختبار 1: التحقق من وجود متغير puzzles
                if (typeof puzzles === 'undefined') {
                    addResult('❌ متغير puzzles غير موجود', 'error');
                    return;
                }
                addResult('✅ متغير puzzles موجود', 'success');

                // اختبار 2: التحقق من هيكل البيانات
                const languages = ['java', 'html', 'css'];
                const difficulties = ['easy', 'medium', 'hard'];

                languages.forEach(lang => {
                    if (!puzzles[lang]) {
                        addResult(`❌ لغة ${lang} غير موجودة`, 'error');
                        return;
                    }
                    addResult(`✅ لغة ${lang} موجودة`, 'success');

                    difficulties.forEach(diff => {
                        if (!puzzles[lang][diff]) {
                            addResult(`❌ مستوى ${diff} للغة ${lang} غير موجود`, 'error');
                            return;
                        }
                        const count = puzzles[lang][diff].length;
                        addResult(`✅ ${lang} - ${diff}: ${count} ألغاز`, 'success');
                    });
                });

                // اختبار 3: التحقق من الدوال
                if (typeof getAllPuzzles === 'undefined') {
                    addResult('❌ دالة getAllPuzzles غير موجودة', 'error');
                } else {
                    addResult('✅ دالة getAllPuzzles موجودة', 'success');
                    
                    // اختبار الدالة
                    const testPuzzles = getAllPuzzles('java', 'easy');
                    if (testPuzzles && testPuzzles.length > 0) {
                        addResult(`✅ دالة getAllPuzzles تعمل: ${testPuzzles.length} ألغاز`, 'success');
                    } else {
                        addResult('❌ دالة getAllPuzzles لا تعمل بشكل صحيح', 'error');
                    }
                }

                if (typeof getRandomPuzzle === 'undefined') {
                    addResult('❌ دالة getRandomPuzzle غير موجودة', 'error');
                } else {
                    addResult('✅ دالة getRandomPuzzle موجودة', 'success');
                }

                // اختبار 4: التحقق من صحة بنية الألغاز
                let totalPuzzles = 0;
                languages.forEach(lang => {
                    difficulties.forEach(diff => {
                        const puzzleList = puzzles[lang][diff];
                        puzzleList.forEach((puzzle, index) => {
                            totalPuzzles++;
                            const requiredFields = ['title', 'description', 'code', 'question', 'answer', 'hint', 'solution'];
                            const missingFields = requiredFields.filter(field => !puzzle[field]);
                            
                            if (missingFields.length > 0) {
                                addResult(`❌ ${lang}-${diff}-${index}: حقول مفقودة: ${missingFields.join(', ')}`, 'error');
                            }
                        });
                    });
                });

                addResult(`📊 إجمالي الألغاز: ${totalPuzzles}`, 'info');

                // النتيجة النهائية
                if (allTestsPassed) {
                    addResult('🎉 جميع الاختبارات نجحت! ملف الألغاز يعمل بشكل مثالي', 'success');
                } else {
                    addResult('⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'error');
                addResult(`<pre>${error.stack}</pre>`, 'error');
            }
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        window.onload = runTests;
    </script>
</body>
</html>
