# 🚀 دليل التشغيل السريع - Code Puzzle Game

## 🎯 أسرع طريقة لتشغيل اللعبة

### ✨ الطريقة الأسهل (مستحسنة)
```
1. انقر مرتين على ملف: start_game.bat
2. اختر الخيار رقم 2 (تشغيل مع خادم محلي)
3. انتظر حتى تفتح اللعبة تلقائياً
```

### 🔧 إذا لم تعمل الطريقة الأولى

#### الطريقة البديلة 1:
```
1. انقر مرتين على: start.html
2. اضغط "تشغيل مباشر"
```

#### الطريقة البديلة 2:
```
1. انقر مرتين على: index.html مباشرة
```

#### الطريقة البديلة 3:
```
1. افتح Command Prompt
2. اكتب: python -m http.server 8000
3. افتح المتصفح على: http://localhost:8000
```

---

## 🔍 فحص المشاكل

### إذا لم تعمل اللعبة:
1. **افتح ملف:** `check_game.html`
2. **سيفحص جميع الملفات تلقائياً**
3. **اتبع التعليمات المعروضة**

### الملفات المطلوبة:
- ✅ `index.html` - الصفحة الرئيسية
- ✅ `styles.css` - التصميم
- ✅ `script.js` - منطق اللعبة
- ✅ `puzzles.js` - الألغاز
- ✅ `config.js` - الإعدادات

---

## 🎮 كيفية اللعب

### 1. اختيار الإعدادات
- **مستوى الصعوبة:** مبتدئ / متوسط / متقدم
- **لغة البرمجة:** Java / HTML / CSS

### 2. بدء اللعبة
- اضغط "ابدأ اللعبة"
- اقرأ الكود والسؤال بعناية
- اكتب إجابتك واضغط "إرسال"

### 3. استخدام المساعدات
- **تلميح:** للحصول على مساعدة
- **تخطي:** لتجاوز السؤال
- **الحل:** لعرض الإجابة الصحيحة

---

## ⚙️ تخصيص اللعبة

### تعديل الإعدادات:
1. افتح ملف `config.js`
2. عدل الإعدادات حسب رغبتك:
   ```javascript
   scoring: {
       baseScore: 100,        // النقاط الأساسية
       difficultyMultipliers: {
           easy: 1,           // مضاعف السهل
           medium: 1.5,       // مضاعف المتوسط
           hard: 2            // مضاعف الصعب
       }
   }
   ```

### إضافة ألغاز جديدة:
1. افتح ملف `puzzles.js`
2. أضف لغز جديد:
   ```javascript
   {
       title: "عنوان اللغز",
       description: "وصف التحدي",
       code: `// الكود هنا`,
       question: "السؤال؟",
       answer: "الإجابة",
       hint: "تلميح",
       solution: "شرح الحل"
   }
   ```

---

## 🛠️ حل المشاكل الشائعة

### ❌ المشكلة: "الصفحة فارغة"
**✅ الحل:**
- تأكد من تفعيل JavaScript في المتصفح
- استخدم متصفح حديث (Chrome, Firefox, Edge)
- افتح وحدة التحكم (F12) وتحقق من الأخطاء

### ❌ المشكلة: "الألغاز لا تظهر"
**✅ الحل:**
- تأكد من وجود ملف `puzzles.js`
- أعد تحميل الصفحة (F5)
- تحقق من وحدة التحكم للأخطاء

### ❌ المشكلة: "التصميم مكسور"
**✅ الحل:**
- تأكد من وجود ملف `styles.css`
- تحقق من اتصال الإنترنت (للخطوط والأيقونات)
- امسح ذاكرة التخزين المؤقت (Ctrl+F5)

### ❌ المشكلة: "Python غير موجود"
**✅ الحل:**
- حمل Python من: https://python.org
- أو استخدم التشغيل المباشر بدلاً من الخادم

---

## 📁 هيكل المشروع

```
Game/
├── 🎮 ملفات اللعبة الأساسية
│   ├── index.html          # الصفحة الرئيسية
│   ├── styles.css          # التصميم
│   ├── script.js           # منطق اللعبة
│   ├── puzzles.js          # الألغاز
│   └── config.js           # الإعدادات
│
├── 🚀 ملفات التشغيل
│   ├── start.html          # صفحة التشغيل
│   ├── start_game.bat      # تشغيل Windows
│   └── start_game.ps1      # PowerShell
│
├── 🧪 ملفات الاختبار
│   ├── test.html           # اختبار اللعبة
│   └── check_game.html     # فحص الملفات
│
└── 📚 ملفات التوثيق
    ├── README.md           # دليل شامل
    ├── DEVELOPER_GUIDE.md  # دليل المطور
    ├── QUICK_START.md      # هذا الملف
    └── تشغيل_اللعبة.txt    # تعليمات عربية
```

---

## 🎯 نصائح للحصول على أفضل تجربة

### 🏆 للحصول على نقاط أعلى:
- ابدأ بالمستوى السهل لفهم النظام
- استخدم التلميحات بحكمة
- حاول حل الأسئلة بسرعة للحصول على مكافأة الوقت

### 📚 للتعلم الأفضل:
- اقرأ الحلول حتى لو أجبت صحيحاً
- جرب جميع مستويات الصعوبة
- تحدى نفسك في لغات مختلفة

### 🎮 للمتعة أكثر:
- شارك نتائجك مع الأصدقاء
- حاول تحطيم أرقامك القياسية
- اقترح ألغاز جديدة

---

## 📞 الحصول على المساعدة

### 📖 مراجع إضافية:
- **دليل شامل:** `README.md`
- **دليل المطور:** `DEVELOPER_GUIDE.md`
- **تعليمات عربية:** `تشغيل_اللعبة.txt`

### 🔧 أدوات التشخيص:
- **فحص الملفات:** `check_game.html`
- **اختبار اللعبة:** `test.html`
- **وحدة التحكم:** F12 في المتصفح

---

## 🎉 استمتع باللعبة!

**Code Puzzle Game** مصممة لتكون ممتعة وتعليمية في نفس الوقت. 

🎯 **هدفنا:** جعل تعلم البرمجة أكثر متعة وتفاعلاً

💡 **نصيحة أخيرة:** لا تتردد في تجربة جميع الطرق المختلفة لتشغيل اللعبة واختر الأنسب لك!

---

**حظاً موفقاً وتحدي ممتع! 🚀**
